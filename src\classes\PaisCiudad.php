<?php

declare(strict_types=1);

namespace App\classes;

use Exception;
use PDO;
use PDOException;

class PaisesCiudades
{
	// --- Atributos ---
	private ?int    $id     = null;
	private ?string $pais   = null;
	private ?string $ciudad = null;
	private ?int    $estado = null;
	
	/**
	 * Constructor: Inicializa las propiedades del objeto.
	 */
	public function __construct()
	{
		$this->id     = 0;
		$this->pais   = null;
		$this->ciudad = null;
		$this->estado = 1; // Estado activo por defecto
	}
	
	/**
	 * Método estático para construir un objeto PaisesCiudades desde un array.
	 *
	 * @param array $data Array asociativo con los datos.
	 *
	 * @return self Instancia de PaisesCiudades.
	 * @throws Exception Si ocurre un error durante la construcción.
	 */
	public static function construct(array $data = []): self
	{
		try {
			$objeto         = new self();
			$objeto->id     = isset($data['id']) ? (int)$data['id'] : 0;
			$objeto->pais   = $data['pais'] ?? null;
			$objeto->ciudad = $data['ciudad'] ?? null;
			$objeto->estado = isset($data['estado']) ? (int)$data['estado'] : 1;
			return $objeto;
		} catch (Exception $e) {
			throw new Exception("Error al construir PaisesCiudades: " . $e->getMessage());
		}
	}
	
	// --- Métodos de Acceso a Datos (Estáticos) ---
	
	/**
	 * Obtiene un registro por su ID.
	 *
	 * @param int $id       ID del registro.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return self|null Objeto PaisesCiudades o null si no se encuentra.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get(int $id, PDO $conexion): ?self
	{
		try {
			$query = <<<SQL
            SELECT * FROM paises_ciudades WHERE id = :id LIMIT 1
            SQL;
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":id", $id, PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);
			
			return $resultado ? self::construct($resultado) : null;
			
		} catch (PDOException $e) {
			throw new Exception("Error al obtener PaisesCiudades (ID: $id): " . $e->getMessage());
		}
	}
	
	/**
	 * Obtiene una lista de todos los países y ciudades.
	 *
	 * @param PDO  $conexion    Conexión PDO.
	 * @param bool $soloActivos True para obtener solo registros activos.
	 *
	 * @return array Array de objetos PaisesCiudades.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get_list(PDO $conexion, bool $soloActivos = true): array
	{
		try {
			$sqlWhere = $soloActivos ? "WHERE estado = 1" : "";
			$query    = <<<SQL
            SELECT * FROM paises_ciudades {$sqlWhere} ORDER BY pais, ciudad
            SQL;
			
			$statement  = $conexion->prepare($query);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);
			
			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}
			return $listado;
			
		} catch (PDOException $e) {
			throw new Exception("Error al obtener lista de PaisesCiudades: " . $e->getMessage());
		}
	}
	
	/**
	 * Obtiene una lista de países únicos.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return array Array de strings con los nombres de los países.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get_paises(PDO $conexion): array
	{
		try {
			$query = <<<SQL
            SELECT DISTINCT pais FROM paises_ciudades WHERE estado = 1 ORDER BY pais
            SQL;
			
			$statement = $conexion->prepare($query);
			$statement->execute();
			return $statement->fetchAll(PDO::FETCH_COLUMN);
			
		} catch (PDOException $e) {
			throw new Exception("Error al obtener la lista de países: " . $e->getMessage());
		}
	}
	
	/**
	 * Obtiene una lista de ciudades para un país específico.
	 *
	 * @param string $pais     Nombre del país.
	 * @param PDO    $conexion Conexión PDO.
	 *
	 * @return array Array de strings con los nombres de las ciudades.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get_ciudades_por_pais(string $pais, PDO $conexion): array
	{
		try {
			$query = <<<SQL
            SELECT ciudad FROM paises_ciudades WHERE pais = :pais AND estado = 1 ORDER BY ciudad
            SQL;
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(':pais', $pais, PDO::PARAM_STR);
			$statement->execute();
			return $statement->fetchAll(PDO::FETCH_COLUMN);
			
		} catch (PDOException $e) {
			throw new Exception("Error al obtener ciudades para el país '$pais': " . $e->getMessage());
		}
	}
	
	// --- Getters y Setters ---
	
	public function getId(): ?int
	{
		return $this->id;
	}
	
	public function setId(?int $id): self
	{
		$this->id = $id;
		return $this;
	}
	
	public function getPais(): ?string
	{
		return $this->pais;
	}
	
	public function setPais(?string $pais): self
	{
		$this->pais = $pais;
		return $this;
	}
	
	public function getCiudad(): ?string
	{
		return $this->ciudad;
	}
	
	public function setCiudad(?string $ciudad): self
	{
		$this->ciudad = $ciudad;
		return $this;
	}
	
	public function getEstado(): ?int
	{
		return $this->estado;
	}
	
	public function setEstado(?int $estado): self
	{
		$this->estado = $estado;
		return $this;
	}
}
