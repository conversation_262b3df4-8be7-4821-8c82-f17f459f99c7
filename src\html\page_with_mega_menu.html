@@include('./partial/head.html', {
	"title": "Page with Mega Menu", 
	"css": [
	]}
)
	<!-- BEGIN #app -->
	<div id="app" class="app app-header-fixed app-sidebar-fixed @@if(context.theme == 'material' || context.theme == 'google') { app-with-wide-sidebar}@@if(context.theme == 'google') { app-with-light-sidebar}">
		@@include('./partial/header.html', {"pageWithMegaMenu": "true"})
		@@include('./partial/sidebar.html', {"pageOptionsClass": "active", "pageWithMegaMenuClass": "active"})
		
		<!-- BEGIN #content -->
		<div id="content" class="app-content">
			<!-- BEGIN breadcrumb -->
			<ol class="breadcrumb@@if(context.theme != 'facebook'){ float-xl-end}">
				<li class="breadcrumb-item"><a href="javascript:;">Home</a></li>
				<li class="breadcrumb-item"><a href="javascript:;">Page Options</a></li>
				<li class="breadcrumb-item active">Page with Mega Menu</li>
			</ol>
			<!-- END breadcrumb -->
			<!-- BEGIN page-header -->
			<h1 class="page-header">Page with Mega Menu <small>header small text goes here...</small></h1>
			<!-- END page-header -->
			
			<!-- BEGIN panel -->
			<div class="panel panel-inverse">
				<div class="panel-heading">
					<h4 class="panel-title">Installation Settings</h4>
					<div class="panel-heading-btn">
						<a href="javascript:;" class="btn btn-xs btn-icon btn-default" data-toggle="panel-expand"><i class="fa fa-expand"></i></a>
						<a href="javascript:;" class="btn btn-xs btn-icon btn-success" data-toggle="panel-reload"><i class="fa fa-redo"></i></a>
						<a href="javascript:;" class="btn btn-xs btn-icon btn-warning" data-toggle="panel-collapse"><i class="fa fa-minus"></i></a>
						<a href="javascript:;" class="btn btn-xs btn-icon btn-danger" data-toggle="panel-remove"><i class="fa fa-times"></i></a>
					</div>
				</div>
				<div class="panel-body">
					<p>
						Color Admin use bootstrap 5 default collapse function to toggle / collapse the mega menu for mobile view.
					</p>
				</div>
				<div class="hljs-wrapper">
					<pre><code class="html">&lt;div id="header" class="app-header"&gt;
  &lt;div class="navbar-header"&gt;
    ...
    &lt;button type="button" class="navbar-mobile-toggler" data-bs-toggle="collapse" data-bs-target="#top-navbar"&gt;
      &lt;span class="fa-stack fa-lg text-dark"&gt;
        &lt;i class="far fa-square fa-stack-2x"&gt;&lt;/i&gt;
        &lt;i class="fa fa-cog fa-stack-1x mt-1px"&gt;&lt;/i&gt;
      &lt;/span&gt;
    &lt;/button&gt;
  &lt;/div&gt;

  &lt;div class="collapse d-md-block me-auto" id="top-navbar"&gt;
    &lt;div class="navbar-nav"&gt;
      &lt;div class="navbar-item dropdown dropdown-lg@@if(context.theme=='transparent'){ text-gray-900}"&gt;
        &lt;a href="#" class="navbar-link dropdown-toggle pe-md-2" data-bs-toggle="dropdown"&gt;
          &lt;i class="fa fa-th-large fa-fw me-1 me-md-0"&gt;&lt;/i&gt; 
          &lt;span class="d-lg-inline d-md-none"&gt;
            Mega &lt;b class="caret ms-1 ms-md-0"&gt;&lt;/b&gt;
          &lt;/span&gt;
        &lt;/a&gt;
        &lt;div class="dropdown-menu dropdown-menu-lg"&gt;
          ...
        &lt;/div&gt;
      &lt;/div&gt;
      ...
    &lt;/div&gt;
  &lt;/div&gt;
  
  &lt;div class="navbar-nav"&gt;
    ...
  &lt;/div&gt;
&lt;/div&gt;</code></pre>
				</div>
			</div>
			<!-- END panel -->
		</div>
		<!-- END #content -->
		
		@@include('./partial/theme-panel.html')
		@@include('./partial/scroll-top-btn.html')
	</div>
	<!-- END #app -->
	
@@include('./partial/script.html', {
	"script": [
		"../assets/plugins/@highlightjs/cdn-assets/highlight.min.js",
		"../assets/js/demo/render.highlight.js"
	]}
)