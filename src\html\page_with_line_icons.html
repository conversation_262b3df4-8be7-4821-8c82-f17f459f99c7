@@include('./partial/head.html', {
	"title": "Page with Line Icons", 
	"css": [
		"../assets/plugins/simple-line-icons/css/simple-line-icons.css"
	]}
)
	<!-- BEGIN #app -->
	<div id="app" class="app app-header-fixed app-sidebar-fixed @@if(context.theme == 'material' || context.theme == 'google') { app-with-wide-sidebar}@@if(context.theme == 'google') { app-with-light-sidebar}">
		@@include('./partial/header.html')
		@@include('./partial/sidebar.html', {"pageOptionsClass": "active", "pageWithLineIconsClass": "active", "icon": "lineicons"})
		
		<!-- BEGIN #content -->
		<div id="content" class="app-content">
			<!-- BEGIN breadcrumb -->
			<ol class="breadcrumb@@if(context.theme != 'facebook'){ float-xl-end}">
				<li class="breadcrumb-item"><a href="javascript:;">Home</a></li>
				<li class="breadcrumb-item"><a href="javascript:;">Page Options</a></li>
				<li class="breadcrumb-item active">Page with Line Icons</li>
			</ol>
			<!-- END breadcrumb -->
			<!-- BEGIN page-header -->
			<h1 class="page-header">Page with Line Icons <small>header small text goes here...</small></h1>
			<!-- END page-header -->
			
			<!-- BEGIN panel -->
			<div class="panel panel-inverse">
				<div class="panel-heading">
					<h4 class="panel-title">Installation Settings</h4>
					<div class="panel-heading-btn">
						<a href="javascript:;" class="btn btn-xs btn-icon btn-default" data-toggle="panel-expand"><i class="fa fa-expand"></i></a>
						<a href="javascript:;" class="btn btn-xs btn-icon btn-success" data-toggle="panel-reload"><i class="fa fa-redo"></i></a>
						<a href="javascript:;" class="btn btn-xs btn-icon btn-warning" data-toggle="panel-collapse"><i class="fa fa-minus"></i></a>
						<a href="javascript:;" class="btn btn-xs btn-icon btn-danger" data-toggle="panel-remove"><i class="fa fa-times"></i></a>
					</div>
				</div>
				<div class="panel-body">
					<p>
						Sample sidebar created with <a href="https://simplelineicons.github.io/" target="_blank">Simple Line Icons</a>. Below is the required <b>css</b> and <b>HTML</b> element for simple line icons.
					</p>
				</div>
				<div class="hljs-wrapper">
					<pre><code class="html">&lt;!-- required css --&gt;
&lt;link href="../assets/plugins/simple-line-icons/css/simple-line-icons.css" rel="stylesheet" /&gt;

&lt;!-- sidebar icon --&gt;
&lt;a href="" class="menu-link"&gt;
  &lt;div class="menu-icon"&gt;
    &lt;i class="icon-rocket"&gt;&lt;/i&gt;
  &lt;/div&gt;
  ...
&lt;/a&gt;
</code></pre>
				</div>
			</div>
			<!-- END panel -->
		</div>
		<!-- END #content -->
		
		@@include('./partial/theme-panel.html')
		@@include('./partial/scroll-top-btn.html')
	</div>
	<!-- END #app -->
	
@@include('./partial/script.html', {
	"script": [
		"../assets/plugins/@highlightjs/cdn-assets/highlight.min.js",
		"../assets/js/demo/render.highlight.js"
	]}
)