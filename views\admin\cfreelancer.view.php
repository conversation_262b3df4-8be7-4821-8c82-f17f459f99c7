<?php
#region region DOCS
/** @var Freelancer|null $freelancer The fetched Freelancer object, or null if not found/error */
/** @var PDO $conexion PDO connection (likely available from preparar.php) */
/** @var FreelancerEspecializacion[] $areas */

// Use the Freelancer class namespace
use App\classes\Freelancer;
use App\classes\FreelancerEspecializacion;

// Start session if not already started (might be handled in preparar.php, but good practice)
if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

// Note: Feedback variables ($success_text, $error_text etc.) might be initialized
// in preparar.php. This view doesn't directly use them but relies on the controller
// setting flash messages which might be displayed via JS in core_js_adm.view.php

#endregion DOCS
?>
<!DOCTYPE html>
<html lang="es" class="dark-mode"> <?php // Match lfreelancers.view.php ?>
<head>
	<meta charset="utf-8"/>
	<title><?php echo APP_NAME; ?> | Perfil Freelancer</title> <?php // Updated title ?>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="Detalle del perfil del freelancer" name="description"/> <?php // Updated description ?>
	<meta content="" name="author"/>
	
	<?php #region HEAD ?>
	<?php // Include the common admin head elements (CSS, etc.) ?>
	<?php require_once __ROOT__ . '/views/admin/general/head.view.php'; ?>
	<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
	<style>
        /* Specific styles for profile details within the admin panel */
        .profile-section {
            margin-bottom: 1.5rem;
            padding-bottom: 1.5rem;
            border-bottom: 1px solid var(--bs-border-color); /* Use theme variable */
        }

        .profile-section:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }

        .profile-section h5 {
            margin-bottom: 1rem;
            color: var(--bs-primary); /* Use theme variable */
            font-size: 1.1rem; /* Slightly larger heading */
            font-weight: 600;
        }

        .profile-label {
            font-weight: 600; /* Bolder label */
            color: var(--bs-body-color); /* Theme text color */
            opacity: 0.8; /* Slightly muted */
        }

        .profile-value {
            color: var(--bs-body-color); /* Theme text color */
        }

        .profile-value a {
            word-break: break-all; /* Ensure long URLs wrap */
            color: var(--bs-link-color);
        }

        .profile-value a:hover {
            color: var(--bs-link-hover-color);
        }

        .profile-value .badge {
            margin-right: 5px;
            margin-bottom: 5px;
        }
	</style>
	<?php #endregion HEAD ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
	<span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/admin/general/header.view.php'; ?>
	
	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/admin/general/sidebar.view.php'; ?>
	
	<!-- BEGIN #content -->
	<div id="content" class="app-content">
		
		<?php if ($freelancer): ?>
			<?php #region region PAGE HEADER ?>
			<div class="d-flex align-items-center mb-3">
				<div>
					<h4 class="mb-0">
						Perfil de Freelancer: <?php echo htmlspecialchars($freelancer->getNombreCompleto() ?? 'N/A', ENT_QUOTES, 'UTF-8'); ?>
					</h4>
					<p class="mb-0 text-muted">Detalles del perfil profesional y de contacto.</p>
				</div>
				<div class="ms-auto"> <?php // Aligned to the right ?>
					<a href="lfreelancers" class="btn btn-secondary"> <?php // Changed btn-outline-theme to btn-secondary ?>
						<i class="fa fa-arrow-left fa-fw me-1"></i> Volver a la Lista <?php // Added fa-fw class to icon ?>
					</a>
				</div>
			</div>
			<hr>
			<?php #endregion PAGE HEADER ?>
			
			<?php #region region PANEL PROFILE DETAILS ?>
			<div class="panel panel-inverse mt-3 no-border-radious"> <?php // Use panel structure ?>
				<div class="panel-heading no-border-radious">
					<h4 class="panel-title">Detalles del Perfil</h4>
					<?php // Optional: Add panel tools if needed ?>
				</div>
				<div class="panel-body"> <?php // Profile sections go inside panel body ?>
					
					<!-- Personal Information Section (Matches Tab 1 from form_freelancers.view.php) -->
					<section class="profile-section" id="personal-info">
						<h5><i class="bi bi-person-fill me-2"></i>Información Personal</h5>
						<div class="row mb-2">
							<div class="col-md-3 profile-label">Nombre Completo:</div>
							<div class="col-md-9 profile-value"><?php echo htmlspecialchars($freelancer->getNombreCompleto() ?? 'N/A', ENT_QUOTES, 'UTF-8'); ?></div>
						</div>
						<div class="row mb-2">
							<div class="col-md-3 profile-label">Tipo Documento:</div>
							<div class="col-md-9 profile-value"><?php echo htmlspecialchars($freelancer->getTipoDocumento() ?? 'N/A', ENT_QUOTES, 'UTF-8'); ?></div>
						</div>
						<div class="row mb-2">
							<div class="col-md-3 profile-label">Núm. Documento:</div>
							<div class="col-md-9 profile-value"><?php echo htmlspecialchars($freelancer->getDocumentoIdentidad() ?? 'N/A', ENT_QUOTES, 'UTF-8'); ?></div>
						</div>
						<div class="row mb-2">
							<div class="col-md-3 profile-label">Correo Electrónico:</div>
							<div class="col-md-9 profile-value">
								<a href="mailto:<?php echo htmlspecialchars($freelancer->getCorreoElectronico() ?? '', ENT_QUOTES, 'UTF-8'); ?>">
									<?php echo htmlspecialchars($freelancer->getCorreoElectronico() ?? 'N/A', ENT_QUOTES, 'UTF-8'); ?>
								</a>
							</div>
						</div>
						<div class="row mb-2">
							<div class="col-md-3 profile-label">Número Teléfono:</div>
							<div class="col-md-9 profile-value"><?php echo htmlspecialchars($freelancer->getNumeroTelefono() ?? 'N/A', ENT_QUOTES, 'UTF-8'); ?></div>
						</div>
						<div class="row mb-2">
							<div class="col-md-3 profile-label">Dirección:</div>
							<div class="col-md-9 profile-value"><?php echo htmlspecialchars($freelancer->getDireccionCompleta() ?? 'N/A', ENT_QUOTES, 'UTF-8'); ?></div>
						</div>
						<div class="row mb-2">
							<div class="col-md-3 profile-label">Ciudad Residencia:</div>
							<div class="col-md-9 profile-value"><?php echo htmlspecialchars($freelancer->getCiudadResidencia() ?? 'N/A', ENT_QUOTES, 'UTF-8'); ?></div>
						</div>
						<div class="row mb-2">
							<div class="col-md-3 profile-label">País Residencia:</div>
							<div class="col-md-9 profile-value"><?php echo htmlspecialchars($freelancer->getPaisResidencia() ?? 'N/A', ENT_QUOTES, 'UTF-8'); ?></div>
						</div>

						<!-- Removed consent information as it's on a separate tab in the form -->
					</section>
					
					<!-- Professional Experience Section (UPDATED) -->
					<section class="profile-section" id="professional-experience">
						<h5><i class="bi bi-briefcase-fill me-2"></i>Experiencia Profesional</h5>
						<?php
						// Get the experience object - Assume it's loaded with the freelancer
						?>
						<?php if ($freelancer->getExperiencia()): ?>
							<div class="row mb-2">
								<div class="col-md-3 profile-label">Resumen Profesional:</div>
								<div class="col-md-9 profile-value">
									<?php echo nl2br(htmlspecialchars($freelancer->getExperiencia()->getResumenProfesional() ?? 'N/A', ENT_QUOTES, 'UTF-8')); ?>
								</div>
							</div>
							<div class="row mb-2">
								<div class="col-md-3 profile-label">Certificaciones Relevantes:</div>
								<div class="col-md-9 profile-value">
									<?php echo nl2br(htmlspecialchars($freelancer->getExperiencia()->getCertificacionesRelevantes() ?? 'N/A', ENT_QUOTES, 'UTF-8')); ?>
								</div>
							</div>
							<div class="row mb-2">
								<div class="col-md-3 profile-label">Proyectos Relevantes:</div>
								<div class="col-md-9 profile-value">
									<?php echo nl2br(htmlspecialchars($freelancer->getExperiencia()->getProyectosRelevantes() ?? 'N/A', ENT_QUOTES, 'UTF-8')); ?>
								</div>
							</div>

						<?php else: ?>
							<p class="text-muted"><i>No se encontró información de experiencia profesional para este freelancer.</i></p>
						<?php endif; ?>

						<!-- Language Information Display -->
						<?php if (isset($freelancer_idiomas) && !empty($freelancer_idiomas)): ?>
							<hr class="my-3">
							<div class="row mb-2">
								<div class="col-12">
									<div class="profile-label mb-3">Idiomas:</div>
									<div class="d-flex flex-wrap gap-2">
										<?php foreach ($freelancer_idiomas as $freelancer_idioma): ?>
											<?php
											try {
												$idioma = $freelancer_idioma->get_idioma($conexion);
												$nivel = $freelancer_idioma->getNivel();

												// Define badge colors based on proficiency level
												$badge_class = 'bg-secondary';
												switch ($nivel) {
													case 'Nativo':
														$badge_class = 'bg-success';
														break;
													case 'Avanzado':
														$badge_class = 'bg-primary';
														break;
													case 'Intermedio':
														$badge_class = 'bg-info';
														break;
													case 'Básico':
														$badge_class = 'bg-warning text-dark';
														break;
												}
											} catch (Exception $e) {
												error_log("Error loading language info for freelancer ID {$freelancer->getId()}: " . $e->getMessage());
												continue;
											}
											?>
											<?php if ($idioma): ?>
												<div class="mb-3" style="max-width: 150px;">
													<div class="card border-0 shadow-sm h-100" style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);">
														<div class="card-body p-3 text-center">
															<div class="mb-2">
																<i class="bi bi-translate text-primary fs-4"></i>
															</div>
															<h6 class="card-title mb-2 fw-bold text-black-500">
																<?php echo htmlspecialchars($idioma->getNombre() ?? 'N/A', ENT_QUOTES, 'UTF-8'); ?>
															</h6>
															<span class="badge <?php echo $badge_class; ?> fs-12px px-3 py-2">
																<?php echo htmlspecialchars($nivel ?? 'N/A', ENT_QUOTES, 'UTF-8'); ?>
															</span>
														</div>
													</div>
												</div>
											<?php endif; ?>
										<?php endforeach; ?>
									</div>
								</div>
							</div>
						<?php elseif (isset($freelancer_idiomas)): ?>
							<hr class="my-3">
							<div class="row mb-2">
								<div class="col-12">
									<div class="profile-label mb-3">Idiomas:</div>
									<div class="alert alert-info d-flex align-items-center">
										<i class="bi bi-info-circle-fill me-2"></i>
										<div>No se han registrado idiomas para este freelancer.</div>
									</div>
								</div>
							</div>
						<?php endif; ?>
					</section>

					<!-- Portfolio de servicios Section -->
					<section class="profile-section" id="servicios-info">
						<h5><i class="bi bi-gear-fill me-2"></i>Portfolio de servicios</h5>
						<?php
						try {
							$servicios_con_categorias = $freelancer->getServiciosConCategorias($conexion);
						} catch (Exception $e) {
							$servicios_con_categorias = [];
							error_log("Error loading services for freelancer ID {$freelancer->getId()}: " . $e->getMessage());
						}
						?>

						<?php if (!empty($servicios_con_categorias)): ?>
							<div class="row">
								<?php foreach ($servicios_con_categorias as $item): ?>
									<?php
									$servicio = $item['servicio'];
									$categoria = $item['categoria'];
									?>
									<div class="col-md-4 mb-3">
										<div class="card h-100 border-primary">
											<div class="card-body">
												<h6 class="card-title mb-2 text-primary">
													<?php echo htmlspecialchars($servicio->getDescripcion() ?? 'N/A'); ?>
												</h6>
												<p class="card-text text-muted small mb-0">
													<i class="bi bi-tag-fill me-1"></i>
													<strong>Categoría:</strong>
													<?php echo htmlspecialchars($categoria ? $categoria->getDescripcion() : 'Sin categoría'); ?>
												</p>
											</div>
										</div>
									</div>
								<?php endforeach; ?>
							</div>
						<?php else: ?>
							<div class="alert alert-info d-flex align-items-center">
								<i class="bi bi-info-circle-fill me-2"></i>
								<div>
									No se han seleccionado servicios para este freelancer, o no se pudo cargar la información de servicios.
								</div>
							</div>
						<?php endif; ?>
					</section>


					<!-- Availability and Rate Section (Matches Tab 3 from form_freelancers.view.php) -->
					<section class="profile-section" id="availability-tarifa">
						<h5><i class="bi bi-clock-history me-2"></i>Disponibilidad y Tarifa</h5> <?php // Changed icon ?>
						<?php
						// Attempt to get related objects. Assume they are loaded with the freelancer.
						$disponibilidad = $freelancer->getDisponibilidad(); // Assume method exists in Freelancer class
						$tarifa         = $freelancer->getTarifa();         // Assume method exists in Freelancer class
						?>
						
						<?php if ($disponibilidad): ?>
							<div class="row mb-2">
								<div class="col-md-3 profile-label">Disponibilidad:</div>
								<div class="col-md-9 profile-value">
									<?php
									$tiempos_disponibles = [];
									// Check each boolean property (assuming getter methods exist)
									if ($disponibilidad->getDisponibilidadTiempoCompleto()) {
										$tiempos_disponibles[] = 'Tiempo Completo';
									}
									if ($disponibilidad->getDisponibilidadMedioTiempo()) {
										$tiempos_disponibles[] = 'Medio Tiempo';
									}
									if ($disponibilidad->getDisponibilidadPorHoras()) {
										$tiempos_disponibles[] = 'Por Horas';
									}
									
									if (!empty($tiempos_disponibles)) {
										foreach ($tiempos_disponibles as $tiempo) {
											echo '<span class="badge bg-info me-2 mb-2 fs-12px">' . htmlspecialchars($tiempo, ENT_QUOTES, 'UTF-8') . '</span> ';
										}
									} else {
										echo 'N/A';
									}
									?>
								</div>
							</div>
							<div class="row mb-2">
								<div class="col-md-3 profile-label">Modalidad:</div>
								<div class="col-md-9 profile-value">
									<?php
									$modalidades_disponibles = [];
									// Check each boolean property (assuming getter methods exist)
									if ($disponibilidad->getModalidadProyecto()) {
										$modalidades_disponibles[] = 'Por proyecto (Freelance)';
									}
									if ($disponibilidad->getModalidadContratoLargoPlazo()) {
										$modalidades_disponibles[] = 'Contrato a largo plazo (Remoto)';
									}
									if ($disponibilidad->getModalidadHibrido()) {
										$modalidades_disponibles[] = 'Híbrido (Remoto/Presencial)';
									}
									
									if (!empty($modalidades_disponibles)) {
										foreach ($modalidades_disponibles as $modalidad) {
											echo '<span class="badge bg-secondary me-2 mb-2 fs-12px">' . htmlspecialchars($modalidad, ENT_QUOTES, 'UTF-8') . '</span> ';
										}
									} else {
										echo 'N/A';
									}
									?>
								</div>
							</div>
							<div class="row mb-2">
								<div class="col-md-3 profile-label">Huso Horario:</div>
								<div class="col-md-9 profile-value">
									<?php // Use nullsafe operator ?-> and null coalesce ?? - Assuming getNombreCompleto method exists in HusoHorario
									echo htmlspecialchars($disponibilidad->getHusoHorario()?->getHusoHorario() ?? 'N/A', ENT_QUOTES, 'UTF-8');
									?>
								</div>
							</div>
						<?php else: ?>
							<p class="text-muted"><i>No se encontró información de disponibilidad.</i></p>
						<?php endif; ?>
						
						<?php if ($tarifa): ?>
							<?php if ($disponibilidad) echo '<hr class="my-3">'; // Add separator only if disponibilidad was shown ?>
							<div class="row mb-2">
								<div class="col-md-3 profile-label">Tarifa por Hora:</div>
								<div class="col-md-9 profile-value">
									<?php
									// Assuming getTarifaPorHora exists in FreelancerTarifa
									$rate = format_currency_consigno($tarifa->getTarifaPorHora()) . ' COP';
									// Optionally format the rate (e.g., add currency symbol)
									// Example: echo !empty($rate) ? '$' . htmlspecialchars(number_format((float)$rate, 2), ENT_QUOTES, 'UTF-8') : 'N/A';
									echo !empty($rate) ? htmlspecialchars((string)$rate, ENT_QUOTES, 'UTF-8') : 'N/A';
									?>
								</div>
							</div>

						<?php else: ?>
							<?php // Add separator only if disponibilidad was shown *and* tarifa is not found
							if ($disponibilidad) echo '<hr class="my-3">';
							?>
							<p class="text-muted"><i>No se encontró información de tarifa.</i></p>
						<?php endif; ?>
					</section>
					
					<!-- Work References Section -->
					<section class="profile-section" id="work-references">
						<h5><i class="bi bi-person-badge me-2"></i>Referencias Laborales</h5>
						<?php
						// Get the references object - Assume it's loaded with the freelancer via getReferencias()
						$referencias = $freelancer->getReferencias(); // Assuming getReferencias() exists and returns a FreelancerReferencia object or null
						?>
						<?php if ($referencias): ?>
							<!-- BEGIN row -->
							<div class="row">
								<div class="col-md-6 col-xs-12">
									<div class="row mb-2">
										<div class="col-md-4 profile-label">Nombre Completo Ref 1:</div>
										<div class="col-md-8 profile-value">
											<?php echo htmlspecialchars($referencias->getNombreCompletoRef1() ?? 'N/A', ENT_QUOTES, 'UTF-8'); ?>
										</div>
									</div>
									<div class="row mb-2">
										<div class="col-md-4 profile-label">Teléfono Ref 1:</div>
										<div class="col-md-8 profile-value">
											<?php echo htmlspecialchars($referencias->getNumeroTelefonoRef1() ?? 'N/A', ENT_QUOTES, 'UTF-8'); ?>
										</div>
									</div>
								</div>
								<div class="col-md-6 col-xs-12">
									<div class="row mb-2">
										<div class="col-md-4 profile-label">Nombre Completo Ref 2:</div>
										<div class="col-md-8 profile-value">
											<?php echo htmlspecialchars($referencias->getNombreCompletoRef2() ?? 'N/A', ENT_QUOTES, 'UTF-8'); ?>
										</div>
									</div>
									<div class="row mb-2">
										<div class="col-md-4 profile-label">Teléfono Ref 2:</div>
										<div class="col-md-8 profile-value">
											<?php echo htmlspecialchars($referencias->getNumeroTelefonoRef2() ?? 'N/A', ENT_QUOTES, 'UTF-8'); ?>
										</div>
									</div>
								</div>
							</div>
							<!-- END row -->
						
						<?php else: ?>
							<div class="text-muted">No se proporcionaron referencias laborales.</div>
						<?php endif; ?>
					</section>
					<!-- End Work References -->
					
					<!-- Documents Section -->
					<section class="profile-section" id="documents">
						<h5><i class="bi bi-file-earmark-text me-2"></i>Documentos</h5>
						<?php
						// Try to get documents from the freelancer
						// Assuming getDocumentos() returns a FreelancerDocumento object or null
						$documentos = $freelancer->getDocumentos();
						?>
						
						<?php if ($documentos): ?>
							<div class="row mb-3">
								<div class="col-md-12">
									<div class="alert alert-light border p-3">
										<div class="d-flex justify-content-between align-items-center mb-3">
											<span class="text-body fw-semibold">Archivos disponibles para descarga:</span>
										</div>
										
										<div class="list-group">
											<?php
											// Check and provide CV download
											if ($documentos->getCurriculum()):
												$cvFilename = $documentos->getCurriculum();
												$downloadUrl = "download_freelancer?type=cv&id=" . $freelancer->getId();
												?>
												<div class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
													<div>
														<i class="bi bi-file-earmark-text me-2 text-primary"></i>
														<span>Curriculum Vitae</span>
													</div>
													<a href="<?php echo htmlspecialchars($downloadUrl, ENT_QUOTES, 'UTF-8'); ?>" class="btn btn-sm btn-primary" download>
														<i class="fa fa-download me-1"></i> Descargar
													</a>
												</div>
											<?php endif; ?>
											
											<?php
											// Check and provide Certifications download
											if ($documentos->getCertificaciones()):
												$certFilename = $documentos->getCertificaciones();
												$downloadUrl = "download_freelancer?type=cert&id=" . $freelancer->getId();
												?>
												<div class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
													<div>
														<i class="bi bi-patch-check me-2 text-success"></i>
														<span>Certificaciones</span>
													</div>
													<a href="<?php echo htmlspecialchars($downloadUrl, ENT_QUOTES, 'UTF-8'); ?>" class="btn btn-sm btn-success" download>
														<i class="fa fa-download me-1"></i> Descargar
													</a>
												</div>
											<?php endif; ?>
											
											<?php if (!$documentos->getCurriculum() && !$documentos->getCertificaciones()): ?>
												<div class="list-group-item text-muted">No hay documentos disponibles para este freelancer.</div>
											<?php endif; ?>
										</div>
										
										<small class="d-block mt-2 text-muted">
											<i class="bi bi-info-circle me-1"></i> Los documentos se almacenan de forma segura y solo están disponibles para usuarios autorizados.
										</small>
									</div>
								</div>
							</div>
						<?php else: ?>
							<div class="text-muted">No se encontraron documentos asociados a este freelancer.</div>
						<?php endif; ?>
					</section>
				
				</div> <?php // End panel-body ?>
			</div> <?php // End panel ?>
			<?php        #endregion PANEL PROFILE DETAILS ?>
		
		<?php else: ?>
			<?php #region region ERROR MESSAGE ?>
			<div class="alert alert-danger d-flex align-items-center">
				<i class="fa fa-exclamation-triangle fa-2x me-3"></i>
				<div>
					<strong>Error:</strong> No se pudo cargar la información del freelancer o el ID no es válido.
					<a href="lfreelancers" class="alert-link ms-2">Volver a la lista</a>.
				</div>
			</div>
			<?php #endregion ERROR MESSAGE ?>
		<?php endif; ?>
	
	</div>
	<!-- END #content -->
	
	<!-- BEGIN scroll-top-btn -->
	<a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
	<!-- END scroll-top-btn -->

</div>
<!-- END #app -->

<?php #region SCRIPTS ?>
<?php // Include the core JS files (jQuery, Bootstrap, SweetAlert, etc.) ?>
<?php require_once __ROOT__ . '/views/admin/general/core_js_adm.view.php'; ?>
<!-- Add page specific scripts here if needed in the future -->
<?php #endregion SCRIPTS ?>

</body>
</html>
